//pv2Master.js - Gets all the Necessary Master Data for Project
const express = require("express");
const router = express.Router();
const passport = require("passport");
module.exports = router;

//Services
const masterService = require("../services/pv2MasterService");

//Controller
const masterController = require("../controller/pv2MasterController");

//=================================================================================================

router.post("/getProjectServiceType",
    passport.authenticate("jwt"),
    masterController.getProjectServiceType
);  
router.post("/getCurrency",
    passport.authenticate("jwt"),
    masterController.getCurrency
); 
router.post("/getWeekSchedule",
    passport.authenticate("jwt"),
    masterController.getWeekSchedule
); 
router.post("/getHolidayCalendar",
    passport.authenticate("jwt"),
    masterController.getHolidayCalendar
); 


router.post("/getPMFormCustomizeConfig",
    passport.authenticate("jwt"),
    masterController.getPMFormCustomizeConfig
); 

router.post("/getCustomerList",
    passport.authenticate("jwt"),
    masterController.getCustomerList
);  

router.post("/getIndustryList",
    passport.authenticate("jwt"),
    masterController.getIndustryList
);

router.post("/getStatusList",
    passport.authenticate("jwt"),
    masterController.getStatusList
);  

router.post("/getPortfolioCode",
    passport.authenticate("jwt"),
    masterController.getPortfolioCode
);
router.post("/getAccessObject",
    passport.authenticate("jwt"),
    masterController.getAccessObject
); 
router.post("/getProjectRoleMaster",
    passport.authenticate("jwt"),
    masterController.getProjectRoleMaster
); 
router.post("/getTimesheetLocations",
    passport.authenticate("jwt"),
    masterController.getTimesheetLocations
); 
router.post("/getreportsToMaster",
    passport.authenticate("jwt"),
    masterController.getreportsToMaster
); 

router.post("/getEntityList",
    passport.authenticate("jwt"),
    masterController.getEntityList
);
router.post("/getCustomerSuggestionsFromDB",
    passport.authenticate("jwt"),
    masterController.getCustomerSuggestionsFromDB
);
router.post("/getCustomerFromDB",
    passport.authenticate("jwt"),
    masterController.getCustomerFromDB
);

router.post("/getProjectAttachmentsConfigs",
    passport.authenticate("jwt"),
    masterController.getProjectAttachmentsConfigs
);

router.post("/getOpportunitySuggestionFromDB",
    passport.authenticate("jwt"),
    masterController.getOpportunitySuggestionFromDB
);
router.post("/getOpportunityFromDB",
    passport.authenticate("jwt"),
    masterController.getOpportunityFromDB
);
router.post("/getProjectTypeList",
    passport.authenticate("jwt"),
    masterController.getProjectTypeList
);
router.post("/personRoleList",
    passport.authenticate("jwt"),
    masterController.personRoleList
);
router.post("/currencyList",
passport.authenticate("jwt"),
masterController.currencyList
);
router.post("/milestoneStatusList",
passport.authenticate("jwt"),
masterController.milestoneStatusList
)
router.post("/getFricewList",
    passport.authenticate("jwt"),
    masterController.getFricewList
);
router.post("/getServiceTypeList",
    passport.authenticate("jwt"),
    masterController.getServiceTypeList
);
router.post("/getInvoiceTemplate",
    passport.authenticate("jwt"),
    masterController.getInvoiceTemplate
);
router.post("/getWorkLocation",
    passport.authenticate("jwt"),
    masterController.getWorkLocation
);
router.post("/getTemplateMaster",
    passport.authenticate("jwt"),
    masterController.getTemplateMaster
);

router.post("/getCustomerNameList",
    passport.authenticate("jwt"),
    masterController.getCustomerNameList
); 
router.post("/getShiftList",
    passport.authenticate("jwt"),
    masterController.getShiftList
); 


router.post("/getGanttTypeList",
    passport.authenticate("jwt"),
    masterController.getGanttTypeList
);


router.post("/getProfitLossList",
    passport.authenticate("jwt"),
    masterController.getProfitLossList
);

router.post("/getCountryList",
    passport.authenticate("jwt"),
    masterController.getCountryList
);
router.post("/getReasonList",
    passport.authenticate("jwt"),
    masterController.getReasonList
);
router.post("/getPositionList",
    passport.authenticate("jwt"),
    masterController.getPositionList
);
router.post("/getExperianceList",
    passport.authenticate("jwt"),
    masterController.getExperianceList
);
router.post("/getOfficeLocationList",
    passport.authenticate("jwt"),
    masterController.getOfficeLocationList
    );

router.post("/getBillingTypeList",
    passport.authenticate("jwt"),
    masterController.getBillingTypeList
);

router.post("/getPaymentTermsList",
    passport.authenticate("jwt"),
    masterController.getPaymentTermsList
);


router.post("/getDivisionList",
    passport.authenticate("jwt"),
    masterController.getDivisionList
);


router.post("/getSubDivisionList",
    passport.authenticate("jwt"),
    masterController.getSubDivisionList
);

router.post("/getProjectIdentityList",
    passport.authenticate("jwt"),
    masterController.getProjectIdentityList
);

router.post("/getProjectRules",
    passport.authenticate("jwt"),
    masterController.getProjectRules
);
router.post("/getTags",
    passport.authenticate("jwt"),
    masterController.getTags
);

router.post("/getProjectUserSuggestionsFromDB",
    passport.authenticate("jwt"),
    masterController.getProjectUserSuggestionsFromDB
);

router.post("/getRegionList",
    passport.authenticate("jwt"),
    masterController.getRegionList
);

router.post("/getProductCategoryList",
    passport.authenticate("jwt"),
    masterController.getProductCategoryList
);


router.post("/getRevenueTypeList",
    passport.authenticate("jwt"),
    masterController.getRevenueTypeList
);


router.post("/getDeliveryTypeList",
    passport.authenticate("jwt"),
    masterController.getDeliveryTypeList
);


router.post("/getBUList",
    passport.authenticate("jwt"),
    masterController.getBUList
);


router.post("/getVerticalList",
    passport.authenticate("jwt"),
    masterController.getVerticalList
);

router.post("/getBillingAdviceUOMMaster",
    passport.authenticate("jwt"),
    masterController.getBillingAdviceUOMMaster
);

router.post("/getProjectPoMaster",
    passport.authenticate("jwt"),
    masterController.getProjectPoMaster
);

router.post("/getMilestoneTypeList",
    passport.authenticate("jwt"),
    masterController.getMilestoneTypeList
);

router.post("/getProjectListBasedOnRole",
    passport.authenticate("jwt"),
    masterController.getProjectListBasedOnRole
);

router.post("/getProjectCodeBasedOnRole",
    passport.authenticate("jwt"),
    masterController.getProjectCodeBasedOnRole
);

router.post("/getLegalEntityMaster",
    passport.authenticate("jwt"),
    masterController.getLegalEntityMaster
);

router.post("/getValueChainMaster",
    passport.authenticate("jwt"),
    masterController.getValueChainMaster
);

router.post("/getProjectRiskStatusMaster",
    passport.authenticate("jwt"),
    masterController.getProjectRiskStatusMaster
);

router.post("/getProjectPaymentTermsMaster",
    passport.authenticate("jwt"),
    masterController.getProjectPaymentTermsMaster
);

router.post("/getDocumentTypeMaster",
    passport.authenticate("jwt"),
    masterController.getDocumentTypeMaster
);

router.post("/getWorkCityList",
    passport.authenticate("jwt"),
    masterController.getWorkCityList
);

router.post("/getWorkPremisisList",
    passport.authenticate("jwt"),
    masterController.getWorkPremisisList
);
router.post("/getEmploymentType",
    passport.authenticate("jwt"),
    masterController.getEmploymentType
);

router.post("/getEmploymentStatus",
    passport.authenticate("jwt"),
    masterController.getEmploymentStatus
);

router.post("/getDepartment",
    passport.authenticate("jwt"),
    masterController.getDepartment
);

router.post("/getProjectForInboxFilter",
    passport.authenticate("jwt"),
    masterController.getProjectForInboxFilter
);

router.post("/getSubmittedByForInboxFilter",
    passport.authenticate("jwt"),
    masterController.getSubmittedByForInboxFilter
);

router.post("/getStatusMatrix",
    passport.authenticate("jwt"),
    masterController.getStatusMatrix
);
router.post("/getPriorityList",
    passport.authenticate("jwt"),
    masterController.getPriorityList
);

router.post("/getCommercialCatageroiesList",
    passport.authenticate("jwt"),
    masterController.getCommercialCatageroiesList
);
router.post("/getBillingArea",
    passport.authenticate("jwt"),
    masterController.getBillingArea
);
router.post("/getProjectRegion",
    passport.authenticate("jwt"),
    masterController.getProjectRegion
);
router.post("/getProjectEngagement",
    passport.authenticate("jwt"),
    masterController.getProjectEngagement
);

router.post("/getProjectClassification",
    passport.authenticate("jwt"),
    masterController.getProjectClassification
);

router.post("/getloaderConfig",
    passport.authenticate("jwt"),
    masterController.getloaderConfig
);

router.post("/externalRoleList",
    passport.authenticate("jwt"),
    masterController.externalRoleList
);
router.post("/getCommercialCatageroiesList",
    passport.authenticate("jwt"),
    masterController.getCommercialCatageroiesList
);
router.post("/getDivisionListForFilters",
    passport.authenticate("jwt"),
    masterController.getDivisionListForFilters
);


router.post("/getSubDivisionListForFilters",
    passport.authenticate("jwt"),
    masterController.getSubDivisionListForFilters
);

router.post("/getProjectListForFilter",
    passport.authenticate("jwt"),
    masterController.getProjectListForFilter
);

router.post("/getProjectCodeListForFilter",
    passport.authenticate("jwt"),
    masterController.getProjectCodeListForFilter
);

router.post("/getProjectReferenceListForFilter",
    passport.authenticate("jwt"),
    masterController.getProjectReferenceListForFilter
);

router.post("/getRegionListForFilter",
    passport.authenticate("jwt"),
    masterController.getRegionListForFilter
);

router.post("/getWorklocationListForFilter",
    passport.authenticate("jwt"),
    masterController.getWorklocationListForFilter
);


router.post("/getManPowerResourceType",
    passport.authenticate("jwt"),
    masterController.getManPowerResourceType
);
router.post("/getProjectActivityStatus",
    passport.authenticate("jwt"),
    masterController.getProjectActivityStatus
);

router.post("/getProjectActivityPriority",
    passport.authenticate("jwt"),
    masterController.getProjectActivityPriority
);
router.post("/getAllCustomerList",
    passport.authenticate("jwt"),
    masterController.getAllCustomerList
);
router.post("/getTravelTypeList",
    passport.authenticate("jwt"),
    masterController.getTravelTypeList
);
router.post("/getWorkShiftList",
    passport.authenticate("jwt"),
    masterController.getWorkShiftList
);
router.post("/getTimeZoneMasterList",
    passport.authenticate("jwt"),
    masterController.getTimeZoneMasterList
);
router.post("/getMilestoneListDetails",
    passport.authenticate("jwt"),
    masterController.getMilestoneListDetails
);
router.post("/updateInvoiceDetails",
    passport.authenticate("jwt"),
    masterController.updateInvoiceDetails);
router.post("/getAllParentCustomerList",
    passport.authenticate("jwt"),
    masterController.getAllParentCustomerList
);


router.post("/getProjectStatus",
    passport.authenticate("jwt"),
    masterController.getProjectStatus
);


router.post("/getMilestoneStatus",
    passport.authenticate("jwt"),
    masterController.getMilestoneStatus
);
router.post("/getEmployeeClass",
    passport.authenticate("jwt"),
    masterController.getEmployeeClass
);
router.post("/getMilestoneParentCustomerList",
    passport.authenticate("jwt"),
    masterController.getMilestoneParentCustomerList
);  
router.post("/getTaskStatusList",
    passport.authenticate("jwt"),
    masterController.getTaskStatusList
); 
router.post("/getWidgetStatusList",
    passport.authenticate("jwt"),
    masterController.getWidgetStatusList
);  
router.post("/getSoftwareList",
    passport.authenticate("jwt"),
    masterController.getSoftwareList
);

router.post("/getProjectForInboxFilterPA",
    passport.authenticate("jwt"),
    masterController.getProjectForInboxFilterPA
);

router.post("/getCustomerForInboxFilterPA",
    passport.authenticate("jwt"),
    masterController.getCustomerForInboxFilterPA
);