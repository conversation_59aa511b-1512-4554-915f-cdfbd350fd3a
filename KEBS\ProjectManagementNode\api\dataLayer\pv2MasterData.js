const { json } = require("body-parser");
const { logger } = require("../../logger")
const pool = require("../../databaseCon").pool;
const rpool = require("../../databaseCon").rpool;
const mysql = require("mysql");
let mongo = require("../../mongo_conn_native").Connection;
const utilService = require("../utils/pv2UtilityService");
const moment = require("moment");
const _ = require("underscore");

/**
 * 
 * @description Get Service Type Data
 * <AUTHOR>
 * @returns 
 */
module.exports.getProjectServiceTypeData = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT service_type_id AS id, service_type_name AS name,description, billing_advice_type,is_internal,is_resource_loading_applicable, generate_milestone, multiple_milestone,billing_plan_save,billing_plan_forecast, billing_plan_edit, deliverable_based_billing_plan, do_not_consider_forecast, billable_allocation_allowed FROM ${db}.m_service_type WHERE is_active = 1 `)

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving Service Type!"})
    }
}
/**
 * 
 * @description Get Currency
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getCurrency = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT currency_id AS id,currency_code, currency_description AS NAME FROM ${db}.m_currency WHERE is_active=1`)
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving Currency!"})
      }
  }

/**
 * 
 * @param {*} type 
 * @param {*} db 
 * @param {*} user 
 * <AUTHOR> S
 * @desription Get m_pm_form_customize collection
 * @returns 
 */
module.exports.getPMFormCustomizeConfig = async (db, user) => {
    try {
      let database = mongo.client.db(db)
  
      let result = await database.collection('m_pm_form_customize').find().toArray();
  
      return Promise.resolve({ messType: "S", data: result })
    }
    catch (err) {
      logger.info(err)
      return Promise.reject({ messType: "E", error: "Error while retrieving project config!" })
    }
  }

  /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get customer list from m_customer_master
  * @returns 
  */
  module.exports.getCustomerList = async(db, user,params, authorization)=>{
    try{
        let query = `SELECT customer_id AS id, customer_name AS name, customer_code as code, parent_account,legal_entity,paymentTerms
          FROM ${db}.m_customer_master 
          WHERE is_active = 1 AND is_legal_entity = 0
          AND (customer_name IS NOT NULL AND customer_name != '')`
        let query_params = [];
      
        if(params && params.search){
          query += ' AND (customer_name LIKE ?) '
          query_params.push('%' + params.search + '%');
        }
        query += ' ORDER BY id DESC ';
        if(params && params.end && params.start >= 0){
          query += ' LIMIT ? OFFSET ? ;'
          query_params.push(params.end)
          query_params.push(params.start)
        }
          
        let temp = mysql.format(query, query_params);
    
        let result = await rpool(query,query_params);
          return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving Customer List!"})
}
}

/**
 * 
 * @description Get Week schedule
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getWeekSchedule = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id,NAME,week_day FROM ${db}.m_pm_week_schedule WHERE is_active=1`)
        
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving Week Schedule!"})
      }
  }
   


  /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get industry list from m_industry
  * @returns 
  */
  module.exports.getIndustryList = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id, description AS name FROM ${db}.m_industry WHERE is_active = 1 `)
     
        return Promise.resolve({messType:"S", data: result})  
    }
    catch(err){
        logger.info(err)
 
return Promise.resolve({messType:"E", message:"Error while retrieving Industry List!"})
}
}
/**
 * 
 * @description Get Holiday Calendar
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getHolidayCalendar = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id,name FROM ${db}.m_e360_holiday_calender WHERE is_active=1`)

        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving Holiday Calendar!"})
      }
  }

  /**
  * 
  * @param {*} customer 
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get city name from m_city
  * @returns 
  */
  module.exports.getCityName = async(city, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT name FROM ${db}.m_city WHERE id = ? AND is_active = 1 `, [city])
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
      return Promise.resolve("Error While retrieving city name")
    }
  }

  /**
  * 
  * @param {*} customer 
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get country name from m_country
  * @returns 
  */
  module.exports.getCountryName = async(country, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT name FROM ${db}.m_country WHERE id = ? AND is_active = 1 `, [country])
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
      return Promise.resolve("Error While retrieving country name")
    }
  }

  /**
  * 
  * @param {*} customer 
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get state name from m_state
  * @returns 
  */
  module.exports.getStateName = async(state, db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT name FROM ${db}.m_state WHERE id = ? AND is_active = 1 `, [state])
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
      return Promise.resolve("Error While retrieving state name")
    }
  }

  /**
  * 
  * @param {*} gantt_type_id
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get status list using gantt_type_id from m_pm_planning_status
  * @returns 
  */
  module.exports.getStatusList = async( db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id,name, status_color AS color , udrf_summary_card, font_color, label_color, project_screen, object_access FROM ${db}.m_project_status WHERE is_active = 1;`, [])

        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve([])
    }
  }

  /**
  * 
  * @param {*} customer
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription AutoGen Portfolio code using customer 
  * @returns 
  */
  module.exports.getPortfolioCode = async(customer, db, user, authorization)=>{
    try{
        let customerName;
        let newCode;
        let customer_name = await rpool(`SELECT customer_name FROM ${db}.m_customer_master WHERE customer_id = ? AND is_active = 1;`, [customer]);
        let latest_code = await rpool(`SELECT id FROM ${db}.t_pm_portfolio ORDER BY id DESC LIMIT 1;`);
        latest_code = parseInt(latest_code);
        customerName = customer_name[0].customer_name[0];

        if (isNaN(latest_code)) {
          newCode = customerName + "1000";

        } 
        else {
          newCode = customer_name[0] + (latest_code + 1).toString();
        }
        let auto_portfolio_code = newCode;
        return Promise.resolve({messType:"S", data: auto_portfolio_code})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving auto-Gen code for portfolio!"})
    }
  }
  /**
 * 
 * @description Get object access
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getAccessObject = async(db, user, authorization)=>{
  try{
      let result = await rpool(`SELECT id ,name FROM ${db}.m_project_application_object WHERE is_active=1`)
     
      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving object access!"})
    }
}

  /**
 * 
 * @description Get project role master
 * <AUTHOR> K Vijay
 * @returns 
 */
  module.exports.getProjectRoleMaster = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id, name, udrf_summary_card, practice_id, role_type FROM ${db}.m_project_role_master WHERE is_active = 1`)
 
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving project role master!"})
      }
  }

  /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get legal entity list from m_legal_entity
  * @returns 
  */
  module.exports.getEntityList = async(db, user, authorization)=>{
    try{
        let result = await rpool(
        `SELECT entity_id AS id, entity_name AS name FROM ${db}.m_legal_entity WHERE is_active = 1
        AND (entity_name IS NOT NULL AND entity_name != '') ORDER BY entity_id DESC;`)
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving Entity List!"})
}
}

    /**
 * 
 * @description Get location master
 * <AUTHOR> K Vijay
 * @returns 
 */
    module.exports.getTimesheetLocations = async(db, user, authorization)=>{
      try{
          let result = await rpool(``)
          return Promise.resolve({messType:"S", data: result})
      }
      catch(err){
          logger.info(err)
          return Promise.resolve({messType:"E", message:"Error while retrieving location master!"})
        }
    }

   /**
 * 
 * @description Get reports to data for isa
 * <AUTHOR> K Vijay
 * @returns 
 */
   module.exports.getreportsToOldMaster = async(projectID,currentDate,db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT isa.id, isa.associate_id,isa.reports_to,tepd.first_name,tepd.middle_name,tepd.last_name,mprm.name AS role FROM ${db}.t_internal_stakeholders isa
        INNER JOIN ${db}.t_e360_personal_details tepd ON tepd.associate_id=isa.associate_id AND tepd.start_date<=? AND tepd.end_date>=?
        INNER JOIN ${db}.m_project_role_master  mprm ON mprm.id=isa.project_role_id
        WHERE isa.is_active=1 AND tepd.is_active=1 AND isa.item_id=? AND isa.end_date>=?`,[currentDate,currentDate,projectID,currentDate])
        return Promise.resolve(result)
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving report to data for isa!"})
      }
  }
 


 /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription get customer list on search
  * @returns 
  */
 module.exports.getCustomerSuggestionsFromDB = async(searchText, db, user, authorization)=>{
  try{
      logger.info(searchText);
      let result = 
      `SELECT 
      customer_id AS id, 
      customer_name 
      FROM ${db}.m_customer_master 
      WHERE (customer_id LIKE ? OR customer_name LIKE ? OR customer_code LIKE ?)
      AND is_active = 1
      AND is_legal_entity = 0;
      `;
      const customerList = await rpool(mysql.format(result, [searchText, searchText, searchText]));
      return Promise.resolve({value: customerList})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving customers list on search"})
  }
}

/**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription get customer using id
  * @returns 
  */
module.exports.getCustomerFromDB = async(customer_id, db, user, authorization)=>{
  try{
      let result = 
      `SELECT 
      customer_id, 
      customer_name 
      FROM ${db}.m_customer_master 
      WHERE (customer_id = ?)
      AND is_active = 1 
      AND is_legal_entity = 0;
      `;
      const customer = await rpool(mysql.format(result, [[customer_id]]));
      return Promise.resolve({messType:"S", data: customer})
  }
  catch(err){
      logger.info(err)

return Promise.resolve({messType:"E", message:"Error while retrieving customer!"})
}
}


/**
 * 
 * @param {*} db_name 
 * @param {*} user 
 * @description Project Attachment Configuration
 * <AUTHOR> Raam Baskar
 * @version 1.0
 * @returns 
 */
module.exports.getProjectAttachmentConfig = async(db_name) =>{
  try{
      let db = mongo.client.db(db_name)

    let config = await db.collection("m_project_attachment_config").find({is_active:true}).toArray()

    if(config && config.length > 0) {
        return Promise.resolve({
            messType: "S",
            message: "Project Attachments Config retrieved successfully !",
            data: config[0]
        })
    }
    else {
        return Promise.resolve({
          messType: "E",
          message: "Project Attachments config not found !",
          
      })
    }
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", error: err})
  }
}


module.exports.getOpportunitySuggestionFromDB = async(searchText, db, user, authorization)=>{
  try{
    
      let result = `SELECT opportunity_id AS id, opportunity_name AS name FROM ${db}.t_c4c_opportunity 
      WHERE (opportunity_id LIKE ? OR opportunity_name LIKE ?) AND is_active = 1`;

      const opportunity_list = await rpool(mysql.format(result, [searchText, searchText, searchText]));

      return Promise.resolve({value: opportunity_list})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving opportunity list on search"})
  }
}

module.exports.getOpportunityFromDB = async(opportunity_id, db, user, authorization)=>{
  try{
      let result = `SELECT opportunity_id AS id, opportunity_name AS name FROM ${db}.t_c4c_opportunity WHERE is_active = 1 AND opportunity_id =?`;

      let opportunity = await rpool(mysql.format(result, [opportunity_id]));

      return Promise.resolve({messType:"S", data: opportunity})
  }
  catch(err){
      logger.info(err)

return Promise.resolve({messType:"E", message:"Error while retrieving opportunity!"})
}
}
  /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K Vijay
  * @desription Get project type list
  * @returns 
  */
  module.exports.getProjectTypeList = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT project_type_id AS id,description AS name FROM ${db}.m_project_type WHERE is_active=1`)
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving project type list!"})
}
}
  /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K Vijay
  * @desription Get project role list
  * @returns 
  */
  module.exports.personRoleList = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id,name FROM ${db}.m_project_role_master WHERE is_active=1`)
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving project type list!"})
}
}
  /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K Vijay
  * @desription Get currency list
  * @returns 
  */
  module.exports.currencyList = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT currency_id AS id ,currency_code AS name,currency_description FROM ${db}.m_currency WHERE is_active=1`)
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving project type list!"})
}
}


  /**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K Vijay
  * @desription Get milestoneStatusList
  * @returns 
  */
  module.exports.milestoneStatusList = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id,name,status_color,allowed_milestone_status_transitions,allowed_bills_status_transitions FROM ${db}.m_project_status WHERE is_active = 1 AND is_milestone_status = 1`)
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving project type list!"})
}
}



/**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get Fricew list form m_fricew
  * @returns 
  */
module.exports.getFricewList = async(db, user, authorization)=>{
  try{
      let result = await rpool(
      `SELECT id, fricew_name as name
      FROM ${db}.m_fricew
      WHERE is_active = 1;`);

      return Promise.resolve(result)
  }
  catch(err){
      logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving FRICEW List!"})
}
} 

/**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> S
  * @desription Get service type list form m_fricew
  * @returns 
  */
module.exports.getServiceTypeList = async(db, user, authorization)=>{
  try{
      let result = await rpool(
      `SELECT service_type_id, service_type_name
      FROM ${db}.m_service_type
      WHERE is_active = 1;`);

      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving service type List!"})
}
}

/**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K vijay
  * @desription Get getInvoiceTemplate
  * @returns 
  */
module.exports.getInvoiceTemplate = async(db, user, authorization)=>{
  try{
      let result = await rpool(
      `SELECT id,name FROM ${db}.m_pm_invoice_template WHERE is_active=1`);

      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving invoice template"})
}
}

/**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K vijay
  * @desription Get work location
  * @returns 
  */
module.exports.getWorkLocation = async(db, user, authorization)=>{
  try{
      let result = await rpool(
      `SELECT id ,name FROM ${db}.m_project_location WHERE is_active=1`);


      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving work location"})
}
}

/**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K vijay
  * @desription Get template master
  * @returns 
  */
module.exports.getTemplateMaster = async(db, user, authorization)=>{
  try{
      let result = await rpool(
      `SELECT id,template_name,service_type, gantt_type FROM ${db}.m_adapt_template_project_main WHERE is_active=1`);
      for(let item of result){
        item['border_color']='1px solid white'
        item['color']='white'
      }

      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving template master"})
}
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get Shift List for Internal Stakeholders
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getShiftList = async(db, user)=>{
  try{
      let result = await rpool(`SELECT id, shift_name as name FROM ${db}.m_shift WHERE is_active = 1`);

      return Promise.resolve({messType:"S", data: result})

  }
  catch(err){
      logger.info(err)
      return Promise.resolve(err)
  }
}

/**
 * @descrption Get Gantt Type List
 * <AUTHOR> Raam Baskar
 * @version 1.0
 * @returns 
 */
module.exports.getGanttTypeList = async(db)=>{
  try{
      let result = await rpool(`SELECT gantt_type_id AS id, gantt_type_name AS name, gantt_color, gantt_mat_icon , gantt_type_config AS gantt_type, gantt_mat_icon_color FROM ${db}.m_gantt_type WHERE is_active = 1`)
    
      return Promise.resolve(result)
  }
  catch(err){
      logger.info(err)
      return Promise.resolve([])
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Profit & Loss List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getProfitLossList = async (db, user)=>{
  try{
    let result = await rpool(`SELECT mpal.pl_id AS id, mpal.description AS name 
        FROM ${db}.m_p_and_l mpal 
        WHERE mpal.pl_id NOT IN (SELECT DISTINCT mpal1.rollup_id 
        FROM ${db}.m_p_and_l mpal1 WHERE mpal1.is_active = 1 AND mpal1.rollup_id IS NOT NULL) 
        AND mpal.is_active = 1;`);

    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while retrieving Profit & Loss List!"})
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description country List
 * <AUTHOR> S
 * @returns 
 */
module.exports.getCountryList = async (db, user)=>{
  try{
    let result = await rpool(`SELECT id, name FROM ${db}.m_country WHERE is_active = 1;`);

    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while retrieving Country List!"})
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get Project Costing Sheet Status
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getProjectCostingStatus = async(db, user)=>{
  try{
    let result = await rpool(`SELECT * FROM ${db}.m_project_costing_status WHERE is_active = 1`)

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get reason list
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getReasonList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT * FROM ${db}.m_project_reason_master WHERE is_active = 1`)

    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get position list
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getPositionList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT * FROM ${db}.m_role WHERE is_active = 1`)
    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
* @description Get billable type list
 * <AUTHOR> S
 * @returns 
 */
module.exports.getBillingTypeList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT id, name FROM ${db}.m_project_billing_type WHERE is_active = 1`)

    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
return Promise.resolve({messType:"E", error: err, message:"Error while retrieving Billing type List!"})
  }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get experiance list
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getExperianceList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT id,experience_label AS name FROM ${db}.m_rm_work_experience WHERE is_active = 1`)

    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get office location list
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getOfficeLocationList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT id,location AS name FROM ${db}.m_timesheet_office_location WHERE is_active = 1`)

    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get payment terms list
 * <AUTHOR> S
 * @returns 
 */
module.exports.getPaymentTermsList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT id, description AS name,days FROM ${db}.m_payment_terms WHERE is_active = 1`)

    return Promise.resolve({messType:"S", data:result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while retrieving payment terms List!"})
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get Division List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getDivisionList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT id, division_name AS name FROM ${db}.m_e360_division WHERE is_active = 1`)

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}


/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get Division List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getSubDivisionList = async(db, user)=>{
  try{
    let result = await rpool(`SELECT id, sub_division_name AS name FROM ${db}.m_e360_sub_division WHERE is_active = 1`)

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}


/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get ISA Identity Growth/flexi
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getISAIdentity = async(db, user)=>{
  try{
      let result = await rpool(`SELECT * FROM ${db}.m_project_isa_identity WHERE is_active  = 1`);

      return Promise.resolve(result)
  }
  catch(err){
      logger.info(err)
      return Promise.resolve([])
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get Project Rules
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getProjectRules = async(db, user)=>{
  try{
      let result = await rpool(`SELECT * FROM ${db}.m_project_roundoff_type WHERE is_active  = 1`);

      return Promise.resolve(result)
  }
  catch(err){
      logger.info(err)
      return Promise.resolve([])
  }
}
/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get Tags
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getTags = async(db, user)=>{
  try{
      let result = await rpool(`SELECT * FROM ${db}.t_global_tags WHERE application_id=2 AND is_active  = 1`);

      return Promise.resolve(result)
  }
  catch(err){
      logger.info(err)
      return Promise.resolve([])
  }
}

/**
 * 
 * @param {*} searchText 
 * @param {*} date 
 * @param {*} db 
 * @param {*} user 
 * @description Get Project User Suggesstion from DB
 * @returns 
 */
module.exports.getProjectUserSuggestionsFromDB = async(searchText, date, db, user)=>{
  try{
    searchText = "%" + searchText + "%";
    let query = `SELECT DISTINCT tepd.associate_id,tepd.first_name,tepd.middle_name,tepd.last_name, tepd.associate_id AS id, tecd.work_email AS mail 
    FROM ${db}.t_e360_personal_details tepd
    INNER JOIN ${db}.t_e360_contact_details tecd ON tepd.associate_id = tecd.associate_id AND ?>=tecd.start_date AND ?<=tecd.end_date AND tecd.is_active =1 
    INNER JOIN ${db}.t_e360_employment_details teed ON teed.associate_id = tepd.associate_id AND ?>=teed.start_date AND ?<=teed.end_date AND teed.is_active = 1
    INNER JOIN ${db}.t_e360_time_details tetd ON tepd.associate_id = tetd.associate_id AND ?>=tetd.start_date AND ?<=tetd.end_date AND tetd.is_active = 1
    AND tetd.monthly_working_hours!=0 AND tetd.project_available_hours!=0
    WHERE tepd.is_active = 1 AND tecd.is_active = 1 AND teed.is_active = 1 AND teed.employment_status != 3
    AND ?>=tepd.start_date AND ?<=tepd.end_date AND (
    tepd.first_name LIKE ? OR tepd.first_name LIKE ? OR tepd.last_name LIKE ?
    OR tepd.display_name LIKE ? OR tepd.display_name LIKE ? OR tecd.personal_email LIKE ?
    OR tecd.work_email LIKE ? OR tepd.associate_id LIKE ?)`;

    let result = await rpool(query, [
      date,
      date,
      date,
      date,
      date,
      date,
      date,
      date,
      searchText,
      searchText,
      searchText,
      searchText,
      searchText,
      searchText,
      searchText,
      searchText,
    ]);

    for (let res1 of result) {
      res1["displayName"] = await utilService.getTotalName(res1["first_name"], res1["middle_name"], res1["last_name"])
    }

    return Promise.resolve(result)

  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}

/**
 * 
 * @param {*} filter 
 * @param {*} db 
 * @param {*} user 
 * @description Get Filter Form Customer
 * @returns 
 */
module.exports.getPMFormCustomizeConfigwithFilter = async(filter, db ,user)=>{
  try{
    let database = mongo.client.db(db)

    let result = await database.collection('m_pm_form_customize').find(filter).toArray();

    return Promise.resolve(result)

  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}


/**
 * 
 * @param {*} db 
 * @description Get Region List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getRegionList = async(db)=>{
  try{
    let result = await rpool(`SELECT id, region AS name FROM ${db}.m_e360_org_region WHERE is_active = 1`)

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while retrieve Region List"})
  }
}

/**
 * 
 * @param {*} db 
 * @description Get Product Category List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getProductCategoryList = async(db)=>{
  try{
    let result = await rpool(`SELECT product_category_id AS id, product_category_name AS name FROM ${db}.m_product_category WHERE is_active = 1`);

    return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while getting Product Category List!"})
  }
}


/**
 * 
 * @param {*} db 
 * @description Get Revenue Type List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getRevenueTypeList = async(db)=>{
  try{
    let result = await rpool(`SELECT id, name FROM ${db}.m_project_revenue_type WHERE is_active = 1`);

    return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while getting Revenue Type List!"})
  }
}


/**
 * 
 * @param {*} db 
 * @description Get Project Delivery Type
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getDeliveryTypeList = async(db)=>{
  try{
    let result = await rpool(`SELECT id, name FROM ${db}.m_project_delivery_type WHERE is_active = 1`);

    return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while getting Delivery Type List!"})
  }
}

/**
 * 
 * @param {*} verticalIds 
 * @param {*} db 
 * @description Get BU List From Vertical
 * @returns 
 */
module.exports.getBUListFromVertical = async(verticalIds, db)=>{
  try{
    let result = await rpool(`SELECT DISTINCT pl_id AS id, description AS name FROM ${db}.m_p_and_l WHERE pl_id IN (SELECT DISTINCT rollup_id FROM ${db}.m_p_and_l WHERE pl_id IN (10,11,12,13,14,15,16,17,226) AND is_active = 1) AND is_active = 1`,[verticalIds])

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}

/**
 * 
 * @param {*} bu_list 
 * @param {*} db 
 * @description Get Vertical List
 * @returns 
 */
module.exports.getVerticalList = async(bu_list, db)=>{
  try{
    let list= await rpool(`SELECT pl_id AS id, description AS name FROM ${db}.m_p_and_l WHERE rollup_id IN (?) AND is_active = 1`,[bu_list])

    return Promise.resolve(list)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}


/**
 * 
 * @param {*} db 
 * @description Get Billing Advice UOM Master
 * @returns 
 */
module.exports.getBillingAdviceUOMMaster = async(db)=>{
  try{
    let result = await rpool(`SELECT * FROM ${db}.m_project_billing_advice_uom WHERE is_active = 1`)

    return result
  }
  catch(err){
    logger.info(err)
    return []
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} item_id
 * @param {*} quote_id
 * @description Get Project Po Master details
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectPoMaster = async(db,item_id,quote_id)=>{
  try{

    let data = [];
    let query = `SELECT po_number AS id, po_number AS name, quote_id, project_item_id, po_date, po_value, currency, payment_terms, po_ref_no 
                 FROM ${db}.t_project_po_master_details
                WHERE is_active = 1 AND project_item_id = ?`; 
    
    if(quote_id){
      query += `AND quote_id IN (?)`
      data = await rpool (query, [item_id, quote_id]);
    }else{
      data = await rpool (query, [item_id]);
    }


    if(data.length > 0){
      for(let items of data){
        items.po_date = items['po_date'] ? moment(items['po_date']).format("YYYY-MM-DD") : null;
      }
    }
  
    return Promise.resolve({messType:"S", data: data});
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while getting PO Master List!"})
  }
}
/*
* @param {*}  
* @param {*} db 
* @param {*} user 
* <AUTHOR> Rajendaran
* @desription returns the active milestone type
* @returns 
*/
module.exports.getMilestoneTypeList = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id,name FROM ${db}.m_project_milestone_type WHERE is_active = 1`)
        return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving Milestone type list!"}) 
    }
}

/**
* @param {*}  
* @param {*} db 
* @param {*} user 
* @param {*} start
* @param {*} end
* @param {*} search
* <AUTHOR> Rajendaran
* @description Return the item name list based on item ids with optional search functionality.
* @returns 
*/
module.exports.getProjectName = async (db, item_list, start, end, search) => {
  try {
    // Modify the query to include a condition for search if search is not empty
    let query = `SELECT id, item_name as name
                 FROM ${db}.t_project_item 
                 WHERE is_active = 1 
                   AND gantt_type = 2 
                   AND id IN (?)`;

    // Add the search condition if search is not empty
    let queryParams = [item_list];

    if (search !== "") {
      query += " AND item_name LIKE ?";
      queryParams.push(`%${search}%`);
    }

    query += " ORDER BY id ASC LIMIT ?,?";
    queryParams.push(start, end);

    // Execute the query
    let result = await rpool(query, queryParams);
    return Promise.resolve({ messType: "S", data: result });
  }
  catch (err) {
    logger.info(err);
    return Promise.resolve({ messType: "E", message: "Error while retrieving Milestone type list!" });
  }
}


/**
* @param {*}  
* @param {*} db 
* @param {*} user 
* @param {*} start
* @param {*} end
* @param {*} search
* <AUTHOR> Rajendaran
* @description Return the project code list based on item ids with optional search functionality.
* @returns 
*/
module.exports.getProjectCode = async (db, item_list, start, end, search) => {
  try {
    // Modify the query to include a condition for search if search is not empty
    let query = `SELECT id, profit_center as name
                 FROM ${db}.t_project_item 
                 WHERE is_active = 1 
                   AND gantt_type = 2 
                   AND id IN (?)`;

    // Add the search condition if search is not empty
    let queryParams = [item_list];

    if (search !== "") {
      query += " AND profit_center LIKE ?";
      queryParams.push(`%${search}%`);
    }

    query += " ORDER BY id ASC LIMIT ?,?";
    queryParams.push(start, end);

    // Execute the query
    let result = await rpool(query, queryParams);
    return Promise.resolve({ messType: "S", data: result });
  }
  catch (err) {
    logger.info(err);
    return Promise.resolve({ messType: "E", message: "Error while retrieving Project code list!" });
  }
}


/**
* @param {*} db 
* <AUTHOR> Rajendaran
* @desription returns Legal Entity List
* @returns 
*/
module.exports.getLegalEntityMaster = async(db)=>{
  try{
      let result = await rpool(`SELECT entity_id as id,entity_name AS name FROM ${db}.m_legal_entity`)
      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving legal entity's"}) 
  }
}


/**
* @param {*} db 
* <AUTHOR> Rajendaran
* @desription returns project payment terms master data list
* @returns 
*/
module.exports.getProjectPaymentTermsMaster = async(db)=>{
  try{
      let result = await rpool(`SELECT id,description AS name FROM ${db}.m_payment_terms`)
      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving Payment terms"}) 
  }
}

/**
* @param {*} db 
* @param {*} source 
* <AUTHOR> Rajendaran
* @desription Get Document type based on the source
* @returns 
*/
module.exports.getDocumentTypeMaster = async(db, source)=>{
  try{
      let result = await rpool(`SELECT id,document_type AS name FROM ${db}.m_project_document_type_mapping WHERE source_id = ? AND is_active = 1`,[source])
      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving Document types"}) 
  }
}

/**
* @param {*} db 
* <AUTHOR> Aparna V
* @desription returns Master Employment Data
* @returns 
*/
module.exports.getEmploymentType = async(db)=>{
  try{
      let result = await rpool(`SELECT id,type_name AS name FROM ${db}.m_e360_employment_type WHERE is_active = 1`)

      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving EmploymentType"}) 
  }
}

/**
* <AUTHOR>
* @desription returns Project Status id,name,font_color,label_color,status_color
* @returns 
*/
module.exports.getProjectActivityStatus = async(db)=>{
  try{
      let result = await rpool(`Select id,task_label as name,font_color,label_color,status_color from ${db}.m_project_status 
        where is_active=1 and is_for_planning=1`)
      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
     
      return Promise.resolve({messType:"E", message:"Error while retrieving Project Activity Status's"}) 
  }
}

/**
* @param {*} db 
* <AUTHOR> Aparna V
* @desription returns Master Employment Status
* @returns 
*/
module.exports.getEmploymentStatus = async(db)=>{
  try{
      let result = await rpool(`SELECT id,status_name AS name FROM ${db}.m_e360_employment_status WHERE is_active = 1`)
      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving EmploymentStatus"}) 
  }
}

/**
* @param {*} db 
* <AUTHOR> Aparna V
* @desription returns Master Department
* @returns 
*/
module.exports.getDepartment = async(db)=>{
  try{
      let result = await rpool(`SELECT id,name FROM ${db}.m_organization WHERE is_active = 1`)
      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving Department"}) 
  }
}

/**
 * 
 * 
 * @param {*} db 
 * @description Get Region List for utilization report
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getMasterRegionList = async(db,search_query='',region_params=[])=>{
  try{
    let result = await rpool(`SELECT id AS region, region AS region_name FROM ${db}.m_e360_org_region WHERE is_active = 1 ` + search_query + ` `,region_params)

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while retrieve Region List"})
  }
}

/**
 * 
 * @param {*} db 
 * @description Get Project Status Matrix
 * <AUTHOR> Raam Baskar
 */
module.exports.getStatusMatrix = async(db)=>{
  try{
    let result = await rpool(`SELECT * FROM ${db}.m_project_status_matrix`)

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }

}
/**
 * 
 * @param {*} db 
 * @description Get Priority List
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getPriorityList  = async(db)=>{
  try{
    let result = await rpool(`SELECT * FROM ${db}.m_project_priority WHERE is_active = 1`)

    return Promise.resolve(result)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}

/**
 * 
 * 
 * @param {*} db 
 * @param {*} user
 * @description Get Project list for inbox filter
 * <AUTHOR> Rajendaran
 * @returns 
 */
module.exports.getProjectForInboxFilter = async (db, user) => {
  try {
    let result = await rpool(`
      SELECT DISTINCT TPW.project_item_id AS id, TPI.item_name AS name
            FROM ${db}.t_workflow_item TWI
            LEFT JOIN ${db}.t_workflow_header TWH ON TWI.workflow_header_id = TWH.id AND TWH.is_active = 1
            LEFT JOIN ${db}.m_workflow MW ON MW.id = TWH.workflow_id AND MW.is_active = 1
            INNER JOIN ${db}.t_project_workflow TPW ON TPW.workflow_header_id = TWI.workflow_header_id AND TPW.is_active = 1 
            LEFT JOIN ${db}.t_project_item TPI ON TPI.id = TPW.project_item_id AND TPW.project_id = TPI.project_id AND TPI.is_active = 1
             WHERE TWI.is_active = 1 AND TWI.appr_oid = ? AND TWI.status = 'S' AND TWH.is_workflow_complete = 0 AND MW.application_id = 915
      `, [user.oid])

    return Promise.resolve({ messType: "S", data: result })
  }
  catch (err) {
    logger.info(err)
    return Promise.resolve({ messType: "E", error: err, message: "Error while retrieve Region List" })
  }
}

/**
 * 
 * 
 * @param {*} db 
 * @param {*} user
 * @description Get submitted by list for inbox filter
 * <AUTHOR> Rajendaran
 * @returns 
 */
module.exports.getSubmittedByForInboxFilter = async (db, user) => {
  try {
    let result = await rpool(`
            SELECT DISTINCT TPW.submitted_by AS id, RTRIM(LTRIM(CONCAT(COALESCE(CONCAT(TEPD.first_name,' '), ''),COALESCE(CONCAT(TEPD.middle_name,' '), ''),COALESCE(TEPD.last_name, '')))) AS name
            FROM ${db}.t_workflow_item TWI
            LEFT JOIN ${db}.t_workflow_header TWH ON TWI.workflow_header_id = TWH.id AND TWH.is_active = 1
            LEFT JOIN ${db}.m_workflow MW ON MW.id = TWH.workflow_id AND MW.is_active = 1
            INNER JOIN ${db}.t_project_workflow TPW ON TPW.workflow_header_id = TWI.workflow_header_id AND TPW.is_active = 1 
            LEFT JOIN ${db}.t_e360_personal_details TEPD ON TEPD.associate_id = TPW.submitted_by AND TEPD.end_date >= ? AND TEPD.start_date <= ?
             WHERE TWI.is_active = 1 AND TWI.appr_oid = ? AND TWI.status = 'S' AND TWH.is_workflow_complete = 0 AND MW.application_id = 915
      `, [moment().format("YYYY-MM-DD"), moment().format("YYYY-MM-DD"), user.oid])

    return Promise.resolve({ messType: "S", data: result })
  }
  catch (err) {
    logger.info(err)
    return Promise.resolve({ messType: "E", error: err, message: "Error while retrieve Region List" })
  }
}

/**
* @param {*} db 
* @param {*} source 
* <AUTHOR> Aparna V
* @desription Get Work City List
* @returns 
*/
module.exports.getWorkCityList = async(db, source)=>{
  try{
      let result = await pool(`SELECT id,name FROM ${db}.m_city WHERE is_active = 1`,[])

      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving Work City List"}) 
  }
}

module.exports.getBillingArea = async (db) => {
    try {
        let result = await rpool(`SELECT area_id as id, area_name AS name FROM ${db}.m_business_area`);
        return Promise.resolve({ messType: "S", data: result });
    } catch (err) {
        logger.info(err);
        return Promise.resolve({ messType: "E", message: "Error while retrieving billing area" });
    }
};

module.exports.getProjectRegion = async (db) => {
  try {
      let get_region_query = `SELECT mpal.pl_id AS id, mpal.description AS name 
        FROM ${db}.m_p_and_l mpal 
        WHERE mpal.pl_id NOT IN (SELECT DISTINCT mpal1.rollup_id 
        FROM ${db}.m_p_and_l mpal1 WHERE mpal1.is_active = 1 AND mpal1.rollup_id IS NOT NULL) 
        AND mpal.is_active = 1`;

      const region_data = await rpool(get_region_query);
      return Promise.resolve({ messType: "S", data: region_data });
  } catch (err) {
      logger.info(err);
      return Promise.resolve({ messType: "E", message: "Error while retrieving project region" });
  }
};

module.exports.getProjectEngagement = async (db) => {
    try {
        let result = await rpool(`SELECT engagement_id as id, engagement_name AS name FROM ${db}.m_project_engagement`);
        return Promise.resolve({ messType: "S", data: result });
    } catch (err) {
        logger.info(err);
        return Promise.resolve({ messType: "E", message: "Error while retrieving project engagement" });
    }
};

module.exports.getProjectClassification = async (db) => {
    try {
        let result = await rpool(`SELECT classification_id as id, classification_name AS name FROM ${db}.m_project_classification`);
        return Promise.resolve({ messType: "S", data: result });
    } catch (err) {
        logger.info(err);
        return Promise.resolve({ messType: "E", message: "Error while retrieving project classification" });
    }
};
module.exports.getloaderConfig = async (db) => {
  try {
    let database = mongo.client.db(db)
            const config = await database.collection("m_pm_form_customize")
            .findOne({ type: "project-creation", field_name: "loading", is_active: true });
    

    return Promise.resolve({ messType: "S", data: config})
  } catch (err) {
      logger.info(err);
      return Promise.resolve({ messType: "E", message: "Error while retrieving project classification" });
  }
}

/**
* <AUTHOR>
* @desription returns Project Priority
* @returns 
*/
module.exports.getProjectActivityPriority = async (db) => {
  try {
    let result = await rpool(`SELECT id, name, color FROM ${db}.m_project_priority WHERE is_active = 1`);
    
    const processedData = result.map(item => {
      // Default colors
      let textColor = '#808080'; // Grey
      let backgroundColor = '#808080'; // Grey

      // Check if color is present and parse it
      if (item.color) {
        const colorData = JSON.parse(item.color || '[]'); // Default to empty array if parsing fails
        textColor = colorData[0]?.color || textColor;
        backgroundColor = colorData[0]?.['background-color'] || backgroundColor;
      }

      return {
        id: item.id,
        name: item.name,
        text_color: textColor,
        background_color: backgroundColor
      };
    });

    return Promise.resolve({ messType: "S", data: processedData });
  } catch (err) {
    logger.info(err);
    return Promise.resolve({ messType: "E", message: "Error while retrieving Project Priority" });
  }
};


module.exports.externalRoleList = async(db, user, authorization)=>{
  try{
      let result = await rpool(`SELECT id, name, udrf_summary_card, practice_id, role_type FROM ${db}.m_project_role_master WHERE is_active = 1 and role_type='External'`)

      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E"})
    }
}


/**
* @param {*}  
* @param {*} db 
* @param {*} search 
* @param {*} start
* @param {*} end
* <AUTHOR> Rajendaran
* @desription return the item name list for filter
* @returns 
*/
module.exports.getProjectListForFilter = async(db, start, end, search)=>{
  try{

      let result_query = `SELECT id,item_name as name
        FROM ${db}.t_project_item 
        WHERE is_active = 1 AND gantt_type = 2`
      
      if(search && search !== ""){
        result_query += ` AND item_name LIKE '%${search}%' `
      }

      result_query += ` ORDER BY id ASC LIMIT ?,?`

      let result = await rpool(result_query, [start, end])

      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving external role!"})
  }
}


/**
* @param {*} db 
* @param {*} source 
* <AUTHOR> Aparna V
* @desription Get Work Premisis List
* @returns 
*/
module.exports.getWorkPremisisList = async(db, source)=>{
  try{
      let result = await rpool(`SELECT id,name FROM ${db}.m_rm_work_premisis WHERE is_active = 1`,[])

      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)


      return Promise.resolve({messType:"E", message:"Error while retrieving Work Permisis!"}) 
  }
}

/**
* @param {*}  
* @param {*} db 
* @param {*} search 
* @param {*} start
* @param {*} end
* <AUTHOR> Rajendaran
* @desription return the item code list for filter
* @returns 
*/
module.exports.getProjectCodeListForFilter = async(db, start, end, search)=>{
  try{

      let result_query = `SELECT id,profit_center as name
        FROM ${db}.t_project_item 
        WHERE is_active = 1 AND gantt_type = 2`
      
      if(search && search !== ""){
        result_query += ` AND profit_center LIKE '%${search}%' `
      }

      result_query += ` ORDER BY id ASC LIMIT ?,?`

      let result = await rpool(result_query, [start, end])

      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving Work Premisis List"}) 
  }
}

module.exports.getCommercialCatageroiesList = async(db, user, authorization)=>{
  try{
      let result = await rpool(`SELECT id, name  FROM ${db}.m_project_non_billable_category WHERE is_active = 1`)
      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)
        return Promise.resolve({messType:"E", message:"Error while retrieving Category!"})
    }
}
     

/**
* @param {*}  
* @param {*} db 
* @param {*} search 
* @param {*} start
* @param {*} end
* <AUTHOR> Rajendaran
* @desription return the item code list for filter
* @returns 
*/
module.exports.getProjectReferenceListForFilter = async(db, start, end, search)=>{
  try{

      let result_query = `SELECT id,sow_reference_number as name
        FROM ${db}.t_project_item 
        WHERE is_active = 1 AND gantt_type = 2 AND sow_reference_number IS NOT NULL AND sow_reference_number != ""`
      
      if(search && search !== ""){
        result_query += ` AND sow_reference_number LIKE '%${search}%' `
      }

      result_query += ` ORDER BY id ASC LIMIT ?,?`

      let result = await rpool(result_query, [start, end])

      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
      return Promise.resolve({messType:"E", message:"Error while retrieving Project reference list!"}) 
  }
}

/**
  * 
  * @param {*}  
  * @param {*} db 
  * @param {*} user 
  * <AUTHOR> K vijay
  * @desription Get work location
  * @returns 
  */
module.exports.getWorklocationListForFilter = async(db, user, authorization)=>{
  try{
    let result = await rpool(
      `SELECT id ,location AS name FROM ${db}.m_timesheet_office_location WHERE is_active = 1`);


      return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", message:"Error while retrieving work location"})
  }
}

/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @description Get Man Power Resource Type
 * @returns 
 */
module.exports.getManPowerResourceType = async(db, user) =>{
  try{
    let result = await rpool(`SELECT quote_resource_type FROM ${db}.m_billing_plan_resource_type WHERE is_for_isa = 1 AND is_active =1 `);
    
    let val = _.uniq(_.pluck(result,"quote_resource_type"))

    logger.info("Man Power Resource Type")
    logger.info(val)

    return Promise.resolve(val)
  }
  catch(err){
    logger.info(err)
    return Promise.resolve([])
  }
}
module.exports.getAllCustomerList = async(db, user)=>{
  try{
      let query = `SELECT customer_id AS id, customer_name AS name, customer_code as code, parent_account,legal_entity,paymentTerms
        FROM ${db}.m_customer_master 
        WHERE is_active = 1 
        AND (customer_name IS NOT NULL AND customer_name != '')`
      let result = await rpool(query);
        return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving Customer List!"})
}
}

/**
* @param {*} db 
* @param {*} source 
* <AUTHOR> Aparna V
* @desription Get Travel Type List
* @returns 
*/
module.exports.getTravelTypeList = async(db, source)=>{
  try{
      let result = await rpool(`SELECT id,name FROM ${db}.m_rm_travel_type WHERE is_active = 1`,[])

      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)


      return Promise.resolve({messType:"E", message:"Error while retrieving Travel Type!"}) 
  }
}

/**
* @param {*} db 
* @param {*} source 
* <AUTHOR> Aparna V
* @desription Get Work Shift list
* @returns 
*/
module.exports.getWorkShiftList = async(db, source)=>{
  try{
      let result = await rpool(`SELECT * FROM ${db}.m_project_work_shift WHERE is_active = 1`,[])

      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)


      return Promise.resolve({messType:"E", message:"Error while retrieving Work Shift!"}) 
  }
}

/**
* @param {*} db 
* @param {*} source 
* <AUTHOR> Aparna V
* @desription Get TimeZone Master List
* @returns 
*/
module.exports.getTimeZoneMasterList = async(db, source)=>{
  try{
      let result = await rpool(`SELECT id,name FROM ${db}.m_project_timezone WHERE is_active = 1`,[])

      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)


      return Promise.resolve({messType:"E", message:"Error while retrieving Time Zone!"}) 
  }
}
module.exports.getMilestoneListDetails = async (item_id,milestone_id, db) => {
  try {
    let result = await rpool(`SELECT id as milestoneId,milestone_name  AS milestoneName,invoice_date AS invoiceDate FROM ${db}.t_milestones WHERE is_active = 1 AND project_item_id = ? AND id=?`, [item_id,milestone_id]);
    return Promise.resolve({ messType: "S", data: result });
  }
  catch (err) {
    logger.info(err);
    return Promise.resolve({ messType: "E", message: "Error while retrieving Milestones" });
  }
}
module.exports.updateInvoiceDetails = async (date,milestone_id, db) => {
  try {
    let result = await rpool(`UPDATE  ${db}.t_milestones set invoice_date=? WHERE  id=?`, [date,milestone_id]);
    return Promise.resolve({ messType: "S", data: result,message:"Incoice Date Edited Successfully" });
  }
  catch (err) {
    logger.info(err);
    return Promise.resolve({ messType: "E", message: "Error while updating Invoice Date" });
  }}
module.exports.getAllParentCustomerList = async(db, user)=>{
  try{
      let query = `SELECT 
    customer_id AS id, 
    customer_name AS name, 
    customer_code AS code, 
    parent_account, 
    legal_entity, 
    paymentTerms
FROM 
    ${db}.m_customer_master 
WHERE 
    is_active = 1 
    AND customer_name IS NOT NULL 
    AND customer_name != '' 
    AND (parent_account = 0 OR parent_account IS NULL OR parent_account = '' OR parent_account = 'null');`
      let result = await rpool(query);
        return Promise.resolve({messType:"S", data: result})
  }
  catch(err){
      logger.info(err)
return Promise.resolve({messType:"E", message:"Error while retrieving Customer List!"})
}
}



/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Project Status
 * @returns 
 */
module.exports.getProjectStatus = async(db, user, authorization)=>{
  try{
    let result = await rpool(`SELECT id, name FROM ${db}.m_project_status WHERE is_active = 1 AND project_screen = 1;`)

    if(result.length>0)
    {
      return Promise.resolve({messType:"S", data: result})
    }
    else
    {
      return Promise.resolve({messType:"E", data: [], message:"No status found!"})
    }
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while getting Project Status Information!"})
  }
}


/**
 * 
 * @param {*} db 
 * @param {*} user 
 * @param {*} authorization 
 * @description Get Project Status
 * @returns 
 */
module.exports.getMilestoneStatus = async(db, user, authorization)=>{
  try{
    let result = await rpool(`SELECT id, name FROM ${db}.m_project_status WHERE is_active = 1 AND is_milestone_status = 1;`)

    if(result.length>0)
    {
      return Promise.resolve({messType:"S", data: result})
    }
    else
    {
      return Promise.resolve({messType:"E", data: [], message:"No status found!"})
    }
  }
  catch(err){
    logger.info(err)
    return Promise.resolve({messType:"E", error: err, message:"Error while getting Project Status Information!"})
  }
}
module.exports.getEmployeeClass = async(db, source)=>{
  try{
      let result = await rpool(`SELECT id,employee_class as name  FROM ${db}.m_e360_employee_class WHERE is_active = 1`)

      return Promise.resolve({messType:"S", data: result})
    }
    catch(err){
        logger.info(err)


      return Promise.resolve({messType:"E", message:"Error while retrieving Time Zone!"}) 
  }
}
module.exports.getMilestoneParentCustomerList = async (db, user, params, authorization) => {
  try {
    let query = `SELECT 
                    pmcm.customer_id AS id,
                    pmcm.customer_name AS name,
                    pmcm.customer_code AS code,
                    pmcm.parent_account,
                    pmcm.legal_entity,
                    pmcm.paymentTerms
                 FROM ${db}.t_project_item tpi
                 LEFT JOIN ${db}.t_project_header tph 
                    ON tpi.project_id = tph.id 
                    AND tph.is_active = 1
                 LEFT JOIN ${db}.m_customer_master mcm 
                    ON mcm.customer_id = tph.end_customer_id 
                    AND mcm.is_active = 1
                 LEFT JOIN ${db}.m_customer_master pmcm 
                    ON pmcm.customer_id = mcm.parent_account 
                    AND pmcm.is_active = 1
                 WHERE pmcm.customer_name IS NOT NULL 
                   AND pmcm.customer_name != '' AND tpi.gantt_type = 2 
                    AND tpi.is_active = 1`;

    let query_params = [];

    if (params?.search) {
      query += ' AND pmcm.customer_name LIKE ?';
      query_params.push(`%${params.search}%`);
    }

    query += ' GROUP BY pmcm.customer_id ORDER BY pmcm.customer_id DESC';

    if (params?.start !== undefined && params?.end !== undefined && params.start >= 0) {
      query += ' LIMIT ? OFFSET ?';
      query_params.push(params.end, params.start);
    }

    const result = await rpool(query, query_params);

    return Promise.resolve({ messType: "S", data: result });
  } catch (err) {
    logger.info(err);
    return Promise.resolve({ messType: "E", message: "Error while retrieving Customer List!" });
  }
};
module.exports.getTaskStatusList = async( db, user, authorization)=>{
  try{
      let result = await rpool(`SELECT 
    id,name,
    status_color AS color,
    udrf_summary_card,
    font_color,
    is_for_planning,
    label_color,
    task_label,
    is_only_for_task,
    project_screen,
    object_access
FROM ${db}.m_project_status
WHERE is_active = 1;`)

      return Promise.resolve(result)
  }
  catch(err){
      logger.info(err)
      return Promise.resolve([])
  }
}

module.exports.getWidgetStatusList = async( db, user, authorization)=>{
  try{
      let result = await rpool(`SELECT id,COALESCE(task_label, name) AS name, status_color AS color , udrf_summary_card, font_color, label_color, project_screen, object_access FROM ${db}.m_project_status WHERE is_active = 1;`, [])

      return Promise.resolve(result)
  }
  catch(err){
      logger.info(err)
      return Promise.resolve([])
  }
}
module.exports.getSoftwareList = async(db, user, authorization)=>{
    try{
        let result = await rpool(`SELECT id, software_type AS name FROM ${db}.m_project_software_type WHERE is_active = 1 `)
     
        return Promise.resolve({messType:"S", data: result})  
    }
    catch(err){
        logger.info(err)
 
return Promise.resolve({messType:"E", message:"Error while retrieving Industry List!"})
}
}

module.exports.getProjectForInboxFilterPA = async (db, user) => {
  try {
       let result = await rpool(`
        SELECT  DISTINCT tpi.item_name as name,tpi.profit_center as id FROM ${db}.t_project_item tpi
          LEFT JOIN ${db}.t_rm_request trmr ON trmr.project_id = tpi.id AND trmr.is_active=1
          INNER JOIN ${db}.t_rm_allocated_resources trmar ON trmar.request_id = trmr.request_id AND trmar.is_active=1
          INNER JOIN ${db}.t_workflow_header twh ON trmar.workflow_id = twh.id AND twh.is_active = 1
          INNER JOIN ${db}.t_workflow_item twi ON twh.id = twi.workflow_header_id AND twi.is_active = 1
          INNER JOIN ${db}.m_workflow mw ON mw.id = twh.workflow_id AND mw.is_active = 1
          WHERE twi.appr_oid = ? AND twi.status='S' AND twi.is_workflow_item_complete=0 AND twi.is_active=1 AND mw.application_id = 460
        `,[user.aid])

    return Promise.resolve({ messType: "S", data: result })
  }
  catch (err) {
    logger.info(err)
    return Promise.resolve({ messType: "E", error: err, message: "Error while retrieving project List" })
  }
}

module.exports.getCustomerForInboxFilterPA = async (db, user) => {
  try {
       let result = await rpool(`
        SELECT DISTINCT mcm.customer_name as name FROM ${db}.m_customer_master mcm
        LEFT JOIN ${db}.t_rm_request trmr ON mcm.customer_id = trmr.customer AND mcm.is_active = 1
        INNER JOIN ${db}.t_rm_allocated_resources trmar ON trmar.request_id = trmr.request_id AND trmar.is_active=1
        INNER JOIN ${db}.t_workflow_header twh ON trmar.workflow_id = twh.id AND twh.is_active = 1
        INNER JOIN ${db}.t_workflow_item twi ON twh.id = twi.workflow_header_id AND twi.is_active = 1
        INNER JOIN ${db}.m_workflow mw ON mw.id = twh.workflow_id AND mw.is_active = 1
        WHERE twi.appr_oid = ? AND twi.status='S' AND twi.is_workflow_item_complete=0 AND twi.is_active=1 AND mw.application_id = 460
        `,[user.aid])

    return Promise.resolve({ messType: "S", data: result })
  }
  catch (err) {
    logger.info(err)
    return Promise.resolve({ messType: "E", error: err, message: "Error while retrieving customer List" })
  }
}