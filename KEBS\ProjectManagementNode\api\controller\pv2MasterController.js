const logger = require("../../logger").logger;

//dataLayer
const masterData = require("../dataLayer/pv2MasterData");


//services
const masterService = require("../services/pv2MasterService");
const errorService = require("../utils/pv2ErrorService");
//===============================================================================================

/**
 * 
 * @description Get Project Service Type
 * <AUTHOR>
 * @returns 
 */
module.exports.getProjectServiceType = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getProjectServiceTypeData(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Project Service Type"})
    }
}

/**
 * 
 * @description Get Currency
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getCurrency = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getCurrency(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Currency"})
    }
}

/**
 * 
 * @description Get Week Schhedule
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getWeekSchedule = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getWeekSchedule(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Week Schedule"})
    }
}

/**
 * 
 * @description Get Holiday Calendar
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getHolidayCalendar = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getHolidayCalendar(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Holiday Calendar"})
    }
}
module.exports.getPMFormCustomizeConfig = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getPMFormCustomizeConfig(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

module.exports.getCustomerList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let params = req.body || null
        let result = await masterService.getCustomerList(db, req.user,params);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

module.exports.getIndustryList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getIndustryList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

module.exports.getStatusList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getStatusList(db, req.user);

        for(let data of result)
        {
            let udrf_summary_card = data['udrf_summary_card']!=null && data['udrf_summary_card']!="" && data['udrf_summary_card']!="null" ? typeof data['udrf_summary_card']=="string" ? JSON.parse(data['udrf_summary_card']) : "":""

            data['status_color']=udrf_summary_card!=""? udrf_summary_card['color']:"#38c710"
        }
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

module.exports.getPortfolioCode = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        customer = req.body.customer;
        let result = await masterData.getPortfolioCode(customer, db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}
/**
 * 
 * @description Get object access
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getAccessObject = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getAccessObject(db, req.user, req.headers.authorization);
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}



module.exports.getEntityList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getEntityList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving object access"})
    }
}

/**
 * @params searchtext
 * @description retrieve customer on search
 * <AUTHOR> S
 * @returns 
 */
module.exports.getCustomerSuggestionsFromDB = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let searchText=req.body.text;
        await errorService.checkDatabase(db);
        let result = await masterService.getCustomerSuggestionsFromDB(searchText, db, req.user);
        res.json(result)
    }
    catch(err){
        logger.info(err)
       
        res.json({messType:"E", message:"Error while retrieving customers on search"})
    }
}

/**
 * @params id
 * @description retrieve customer based on id
 * <AUTHOR> S
 * @returns 
 */
module.exports.getCustomerFromDB = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let customer_id =req.body.id
        await errorService.checkDatabase(db);
        let result = await masterService.getCustomerFromDB(customer_id, db, req.user);
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving customer"})
    }
}

/**
 * 
 * @description Get project role master
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getProjectRoleMaster = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getProjectRoleMaster(db, req.user, req.headers.authorization);
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving project role master"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Retrive Project Attachment Config
 * <AUTHOR> Raam Baskar
 */
module.exports.getProjectAttachmentsConfigs = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);
        let result = await masterData.getProjectAttachmentConfig(db)
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving customer"})
    }
}


/**
 * 
 * @description Get location master
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getTimesheetLocations = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getTimesheetLocations(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving location master"})
    }
}
/**
 * 
 * @description Get oppurtunityfromdb
 * <AUTHOR> K Vijay
 * @returns 
 */
    

module.exports.getOpportunitySuggestionFromDB = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let searchText=req.body.text;
        await errorService.checkDatabase(db);
        let result = await masterService.getOpportunitySuggestionFromDB(searchText, db, req.user);
        res.json(result)
    }
    catch(err){
        logger.info(err)

        res.json({messType:"E", message:"Error while retrieving opportunity on search"})
    }   
}

/**
 * 
 * @description Get reports to data for isa
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getreportsToMaster = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let projectID=req.body.projectID
        let currentDate=req.body.currentDate
        await errorService.checkDatabase(db);

        let result = await masterService.getreportsToMaster(projectID,currentDate,db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
res.json({messType:"E", message:"Error while retrieving reports to data for isa"})
    }
}
/**
 * 
 * @description Get oppurtunity db
 * <AUTHOR> K Vijay
 * @returns 
 */

     
module.exports.getOpportunityFromDB = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        let opportunity_id =req.body.id
        await errorService.checkDatabase(db);
        let result = await masterService.getOpportunityFromDB(opportunity_id, db, req.user);
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving opportunity"})
       
       
        
    }
}
module.exports.getProjectTypeList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getProjectTypeList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving project type list"})
    }
}
module.exports.personRoleList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.personRoleList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving project role list"})
    }
}
module.exports.currencyList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.currencyList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving currency list"})
    }
}

module.exports.milestoneStatusList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.milestoneStatusList(db, req.user);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving milestoneStatusList"})
    }
}
/**
 * 
 * @description Get FRICEW llist
 * <AUTHOR> S
 * @returns 
 */
module.exports.getFricewList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getFricewList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        
        res.status(500).json(err)
    }
}

/**
 * 
 * @description Get Service type list
 * <AUTHOR> S
 * @returns 
 */
module.exports.getServiceTypeList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getServiceTypeList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @description Get getInvoiceTemplate
 * <AUTHOR> K vijay
 * @returns 
 */
module.exports.getInvoiceTemplate = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getInvoiceTemplate(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}


/**
 * 
 * @description Get work location
 * <AUTHOR> K vijay
 * @returns 
 */
module.exports.getWorkLocation = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getWorkLocation(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @description Get template Master
 * <AUTHOR> K vijay
 * @returns 
 */
module.exports.getTemplateMaster = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getTemplateMaster(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @description Get Customer name list
 * <AUTHOR> S
 * @returns 
 */
module.exports.getCustomerNameList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getCustomerList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @description Get shift list
 * <AUTHOR> K Vijay
 * @returns 
 */
module.exports.getShiftList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getShiftList(db, req.user);

        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}
/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Gantt Type List
 * @version 1.0
 * <AUTHOR> Raam Baskar
 */
module.exports.getGanttTypeList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getGanttTypeList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> Raam Baskar
 * @description P&L List
 * 
 */
module.exports.getProfitLossList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterService.getProfitLossList(db, req.user)
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> S
 * @description Country List
 * 
 */
module.exports.getCountryList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterService.getCountryList(db, req.user)
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> K Vijay
 * @description Reason List
 * 
 */
module.exports.getReasonList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getReasonList(db, req.user)
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> K Vijay
 * @description Position List
 * 
 */
module.exports.getPositionList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getPositionList(db, req.user)
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> K Vijay
 * @description Experiance List
 * 
 */
module.exports.getExperianceList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getExperianceList(db, req.user)
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> K Vijay
 * @description Office Location List
 * 
 */
module.exports.getOfficeLocationList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getOfficeLocationList(db, req.user)
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
/**
 * 
* <AUTHOR> S
 * @description Billing type list
 * 
 */
module.exports.getBillingTypeList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getBillingTypeList(db, req.user)
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * <AUTHOR> S
 * @description Payment terms list
 * 
 */
module.exports.getPaymentTermsList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getPaymentTermsList(db, req.user)
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Division LIst
 * <AUTHOR> Raam Baskar
 */
module.exports.getDivisionList = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getDivisionList(db, req.user)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get SUb Division List
 * <AUTHOR> Raam Baskar
 */
module.exports.getSubDivisionList = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getSubDivisionList(db, req.user)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get SUb Division List
 * <AUTHOR> Raam Baskar
 */
module.exports.getProjectIdentityList = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getISAIdentity(db, req.user)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Rules
 * <AUTHOR> Raam Baskar
 */
module.exports.getProjectRules = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getProjectRules(db, req.user)
    
        res.json(result)

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Tags
 * <AUTHOR> K Vijay
 */
module.exports.getTags = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getTags(db, req.user)
    
        res.json(result)
        
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Project User Suggestion From DB
 */
module.exports.getProjectUserSuggestionsFromDB = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let searchText = req.body.searchText;
        let date = req.body.date;

        let result = await masterData.getProjectUserSuggestionsFromDB(searchText, date, db, req.user)

        res.json({value:result})
    }
    catch(err)
    {
        logger.info(err)
        res.json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Region List
 * <AUTHOR> Raam Baskar
 */
module.exports.getRegionList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let result = await masterData.getRegionList(db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json(err)
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Product Category
 * <AUTHOR> Raam Baskar
 */
module.exports.getProductCategoryList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let result = await masterData.getProductCategoryList(db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json(err)
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Revenue Type List
 * <AUTHOR> Raam Baskar
 */
module.exports.getRevenueTypeList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let result = await masterData.getRevenueTypeList(db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Delivery Type List
 * <AUTHOR> Raam Baskar
 */
module.exports.getDeliveryTypeList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let result = await masterData.getDeliveryTypeList(db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json(err)
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get BU List
 * <AUTHOR> Raam Baskar
 */
module.exports.getBUList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let result = await masterService.getBUList(db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description getVertical List
 */
module.exports.getVerticalList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let bu_list = req.body.bu_list;

        let result = await masterService.getVerticalList(bu_list, db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Billing Advice UOM Master
 */
module.exports.getBillingAdviceUOMMaster = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let result = await masterData.getBillingAdviceUOMMaster(db)

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json([])
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get the project po master details for dropdown.
 * <AUTHOR>
 */
module.exports.getProjectPoMaster = async(req, res)=>{
    try{

        let db = req.user.uad || req.body.db_name;

        let item_id =  req.body.itemId ? req.body.itemId : null;
        let quote_id = req.body.quoteId ? req.body.quoteId : null;

        if(!item_id){
            return Promise.resolve({messType:"E", message:"Project details not found"});
        }

        let result = await masterData.getProjectPoMaster(db,item_id,quote_id);

        res.json(result);

    }catch(err){
        logger.info(err)
        res.json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Milestone Type List Master
 */
module.exports.getMilestoneTypeList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getMilestoneTypeList(db, req.user);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving milestone type"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Name list based on role authorisation
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectListBasedOnRole = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);
        let start = req.body.start ? req.body.start : 0;
        let take =  req.body.end ? req.body.end : 30;
        let search = req.body.search ? req.body.search : "";

        let result = await masterService.getProjectListBasedOnRole(req.user, db, req, start, take, search);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Project Service Type"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Code list based on role authorisation
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectCodeBasedOnRole = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);
        let start = req.body.start ? req.body.start : 0;
        let take =  req.body.end ? req.body.end : 30;
        let search = req.body.search ? req.body.search : ""

        let result = await masterService.getProjectCodeBasedOnRole(req.user, db, req, start, take, search);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Project Service Type"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get legal entity master data 
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getLegalEntityMaster = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getLegalEntityMaster(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving legal entity's"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get the value chain master data
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getValueChainMaster = async(req, res)=>{
    try {
        let result ={
            messType: 'S',
            data: [
            {
                id: 0,
                name: 'O2C'
            },
            {
                id: 1,
                name: 'Q2C'
            }]
        }
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving milestone type"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get the risk status master data
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectRiskStatusMaster = async(req, res)=>{
    try {
        let result ={
            messType: 'S',
            data: [
            {
                id: 0,
                name: 'Secured'
            },
            {
                id: 1,
                name: 'At Risk'
            }]
        }
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving milestone type"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Payment terms master data 
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectPaymentTermsMaster = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getProjectPaymentTermsMaster(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving legal entity's"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Document type based on the source
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getDocumentTypeMaster = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let source = req.body.source ?  req.body.source : null;

        if(!source){
            res.json({messType:"E", message:"source details not found", data:[]})
        }
    
        let result = await masterData.getDocumentTypeMaster(db, source);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving legal entity's"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Employment Type
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getEmploymentType = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getEmploymentType(db);

        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Employment Type"})
    }
}

/**
 * @description Get 
 * <AUTHOR> Project Activity Status
 * @returns 
 */
module.exports.getProjectActivityStatus = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getProjectActivityStatus(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
       
        res.json({messType:"E", message:"Error while retrieving Project Activity's"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Employment Status
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getEmploymentStatus = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getEmploymentStatus(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Employment Status"})
    }
}

/**
 * @description Get 
 * <AUTHOR> Project Priority Status
 * @returns 
 */
module.exports.getProjectActivityPriority = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getProjectActivityPriority(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Employment Status"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Department
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getDepartment = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getDepartment(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Department"})
    }
}


/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Status Matrix
 * <AUTHOR> Raam Baskar
 * @returns 
 */
module.exports.getStatusMatrix = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getStatusMatrix(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Department"})
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Division LIst
 * <AUTHOR>
module.exports.getDivisionListForFilters = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getDivisionList(db, req.user)
    
        res.json({messType: 'S', data: result})

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get SUb Division List
 * <AUTHOR> Rajendaran
 */
module.exports.getSubDivisionListForFilters = async(req,res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getSubDivisionList(db, req.user)
    
        res.json({messType: 'S', data: result})

    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Project list for inbox filter
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectForInboxFilter = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getProjectForInboxFilter(db, req.user);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving project inbox details"})
    }
}

/**
 * @description Get Project  list for filter
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectListForFilter = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);
        let start = req.body.start ? req.body.start : 0;
        let take =  req.body.end ? req.body.end : 30;
        let search = req.body.search ? req.body.search : ""

        let result = await masterData.getProjectListForFilter(db, start, take, search);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Project Service Type"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get submitted by list for inbox filter
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getSubmittedByForInboxFilter = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getSubmittedByForInboxFilter(db, req.user);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving project inbox details"})
    }
}
module.exports.getPriorityList= async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
       
    
        let result = await masterData.getPriorityList(db);
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json([])
    }
}
module.exports.getBillingArea = async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getBillingArea(db);
        res.json(result);
    } catch (err) {
        logger.info(err);
        res.json({ messType: "E", message: "Error while retrieving billing area" });
    }
};

module.exports.getProjectRegion = async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getProjectRegion(db);
        res.json(result);
    } catch (err) {
        logger.info(err);
        res.json({ messType: "E", message: "Error while retrieving project region" });
    }
};

module.exports.getProjectEngagement = async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getProjectEngagement(db);
        res.json(result);
    } catch (err) {
        logger.info(err);
        res.json({ messType: "E", message: "Error while retrieving project engagement" });
    }
};

module.exports.getProjectClassification = async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getProjectClassification(db);
        res.json(result);
    } catch (err) {
        logger.info(err);
        res.json({ messType: "E", message: "Error while retrieving project classification" });
    }
};

module.exports.getloaderConfig= async (req, res) => {
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getloaderConfig(db);
        res.json(result);
    } catch (err) {
        logger.info(err);
        res.json({ messType: "E", message: "Error while retrieving project classification" });
    }
};


module.exports.externalRoleList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.externalRoleList(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json([])
    }
}


/**
 * @description Get Project code  list for filter
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectCodeListForFilter = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);
        let start = req.body.start ? req.body.start : 0;
        let take =  req.body.end ? req.body.end : 30;
        let search = req.body.search ? req.body.search : ""

        let result = await masterData.getProjectCodeListForFilter(db, start, take, search);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json([])
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Work City List
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getWorkCityList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getWorkCityList(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Work City List"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Work Premisis List
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getWorkPremisisList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getWorkPremisisList(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Work Premisis List"})
    }
}

module.exports.getCommercialCatageroiesList = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getCommercialCatageroiesList(db, req.user, req.headers.authorization);
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Commerical Categories list"})
    }
}


/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Project code  list for filter
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getProjectReferenceListForFilter = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);
        let start = req.body.start ? req.body.start : 0;
        let take =  req.body.end ? req.body.end : 30;
        let search = req.body.search ? req.body.search : ""

        let result = await masterData.getProjectReferenceListForFilter(db, start, take, search);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json([])
       
    }
}
        

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Region List
 * <AUTHOR> Rajendran
 */
module.exports.getRegionListForFilter = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;

        let result = await masterData.getRegionList(db)

        res.json({messType: "S", data: result})
    }
    catch(err){
        logger.info(err)
        res.json(err)
    }
}

/**
 * 
 * @description Get work location
 * <AUTHOR> Rajendran
 * @returns 
 */
module.exports.getWorklocationListForFilter = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getWorklocationListForFilter(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Man Power Resource Type
 * <AUTHOR> Raam Baskar 
 */
module.exports.getManPowerResourceType = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getManPowerResourceType(db, req.user);
    
        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.status(500).json(err)
    }
}
module.exports.getAllCustomerList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getAllCustomerList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Travel Type List
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getTravelTypeList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getTravelTypeList(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Travel Type List"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Work Shift List
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getWorkShiftList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getWorkShiftList(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Work Shift List"})
    }
}

/**
 * @param {*} req 
 * @param {*} res 
 * @description Get Time Zone Master List
 * <AUTHOR> Aparna V
 * @returns 
 */
module.exports.getTimeZoneMasterList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getTimeZoneMasterList(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving TimeZone Master List"})
    }
}

module.exports.getMilestoneListDetails = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let item_id=req.body.item_id ?  req.body.item_id : null;
        let milestone_id=req.body.id ?  req.body.id : null;
        let result = await masterData.getMilestoneListDetails(item_id, milestone_id,db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Department"})
    }
}
module.exports.updateInvoiceDetails = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let date=req.body.date?  req.body.date : null;
        let milestone_id=req.body.id ?  req.body.id : null;
        let result = await masterData.updateInvoiceDetails(date, milestone_id,db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Department"})
    }
}
module.exports.getAllParentCustomerList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getAllParentCustomerList(db, req.user);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Project Status
 * <AUTHOR> Raam Baskar
 */
module.exports.getProjectStatus = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getProjectStatus(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Project Service Type"})
    }
}


/**
 * 
 * @param {*} req 
 * @param {*} res 
 * @description Get Milestone Status
 * <AUTHOR> Raam Baskar
 */
module.exports.getMilestoneStatus = async(req, res)=>{
    try{
        let db = req.user.uad || req.body.db_name || null;
        await errorService.checkDatabase(db);

        let result = await masterData.getMilestoneStatus(db, req.user, req.headers.authorization);

        res.json(result)
    }
    catch(err){
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Project Service Type"})
    }
}
module.exports.getEmployeeClass = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
    
        let result = await masterData.getEmployeeClass(db);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving Department"})
    }
}

module.exports.getMilestoneParentCustomerList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let params = req.body || null
        let result = await masterService.getMilestoneParentCustomerList(db, req.user,params);
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}
module.exports.getTaskStatusList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getTaskStatusList(db, req.user);

        for(let data of result)
        {
            let udrf_summary_card = data['udrf_summary_card']!=null && data['udrf_summary_card']!="" && data['udrf_summary_card']!="null" ? typeof data['udrf_summary_card']=="string" ? JSON.parse(data['udrf_summary_card']) : "":""

            data['status_color']=udrf_summary_card!=""? udrf_summary_card['color']:"#38c710"
        }
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}
module.exports.getWidgetStatusList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getWidgetStatusList(db, req.user);

        for(let data of result)
        {
            let udrf_summary_card = data['udrf_summary_card']!=null && data['udrf_summary_card']!="" && data['udrf_summary_card']!="null" ? typeof data['udrf_summary_card']=="string" ? JSON.parse(data['udrf_summary_card']) : "":""

            data['status_color']=udrf_summary_card!=""? udrf_summary_card['color']:"#38c710"
        }
    
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}
module.exports.getSoftwareList = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        let result = await masterData.getSoftwareList(db, req.user); 
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.status(500).json(err)
    }
}

module.exports.getProjectForInboxFilterPA = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getProjectForInboxFilterPA(db, req.user);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving project details"})
    }
}

module.exports.getCustomerForInboxFilterPA = async(req, res)=>{
    try {
        let db = req.user.uad || req.body.db_name;
        
    
        let result = await masterData.getCustomerForInboxFilterPA(db, req.user);
        res.json(result)
    }
    catch (err) {
        logger.info(err)
        res.json({messType:"E", message:"Error while retrieving project details"})
    }
}